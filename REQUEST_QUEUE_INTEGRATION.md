# Request Queue Integration with Token Refresh

## 概述

已成功将 DioNetworkService 的请求队列与 TokenRefreshService 集成，实现了在 token 刷新期间暂停整个队列的功能，避免了多个请求同时触发 token 刷新的问题。

## 主要改动

### 1. RequestQueue 增强 (`lib/shared/data/remote/dio_network_service.dart`)

**新增功能：**
- 暂停/恢复机制：`pause()` 和 `resume()` 方法
- 暂停状态检查：`isPaused` 属性
- 队列处理时考虑暂停状态

**特性：**
- 支持优先级队列（high, normal, low）
- 并发控制（默认最多5个并发请求）
- 队列大小限制（默认最多100个排队请求）
- 支持按标签取消请求

### 2. TokenRefreshManager (`lib/shared/data/remote/dio_network_service.dart`)

**功能：**
- 单例模式管理 token 刷新状态
- 协调请求队列的暂停和恢复
- 防止重复的 token 刷新操作

**方法：**
- `setRequestQueue(RequestQueue queue)`: 设置要管理的队列
- `onTokenRefreshNeeded()`: 开始 token 刷新时调用
- `onTokenRefreshCompleted()`: token 刷新完成时调用

### 3. QueueAwareRequestInterceptor (`lib/shared/data/remote/queue_aware_request_interceptor.dart`)

**新建文件，功能：**
- 继承原有 RequestInterceptor 的所有功能
- 集成 TokenRefreshManager 来控制队列状态
- 在 token 刷新开始时暂停队列
- 在 token 刷新完成时恢复队列

### 4. DioNetworkService 更新

**构造函数变更：**
- 新增 `TokenRefreshService` 参数
- 初始化 `QueueAwareRequestInterceptor`
- 设置 TokenRefreshManager

**新增方法：**
- `pauseQueue()`: 手动暂停队列
- `resumeQueue()`: 手动恢复队列
- `getQueueStatus()`: 获取详细的队列状态（包括暂停状态和 token 刷新状态）

### 5. 依赖注入更新 (`lib/core/di/app_module.dart`)

**变更：**
- DioNetworkService 构造函数新增 TokenRefreshService 参数
- 保持向后兼容性

## 工作流程

### Token 刷新协调流程

1. **请求执行**: 所有 HTTP 请求通过 RequestQueue 排队执行
2. **Token 过期检测**: QueueAwareRequestInterceptor 检测到需要刷新 token 的错误码
3. **队列暂停**: TokenRefreshManager 暂停 RequestQueue 处理新请求
4. **Token 刷新**: 执行实际的 token 刷新操作
5. **请求重试**: 使用新 token 重试失败的请求
6. **队列恢复**: TokenRefreshManager 恢复 RequestQueue 处理

### 并发控制

- **队列暂停期间**: 新请求会排队等待，不会被执行
- **正在执行的请求**: 继续执行，不受暂停影响
- **Token 刷新**: 只有一个 token 刷新操作会执行
- **请求重试**: 失败的请求会使用新 token 自动重试

## 优势

### 1. 避免重复 Token 刷新
- 多个请求同时遇到 token 过期时，只会触发一次刷新
- 其他请求会等待刷新完成后使用新 token

### 2. 更好的性能
- 并发控制避免了过多的同时请求
- 队列机制提供了更好的资源管理

### 3. 优先级支持
- 重要请求可以设置高优先级
- 系统请求可以优先处理

### 4. 灵活的控制
- 可以手动暂停/恢复队列
- 支持按标签取消特定请求
- 详细的状态监控

## 使用示例

```dart
// 获取网络服务
final networkService = getIt<NetworkService>() as DioNetworkService;

// 查看队列状态
final status = networkService.getQueueStatus();
print('Queue size: ${status['queueSize']}');
print('Active requests: ${status['activeRequests']}');
print('Is paused: ${status['isPaused']}');
print('Token refresh in progress: ${status['tokenRefreshInProgress']}');

// 手动控制队列
networkService.pauseQueue();  // 暂停队列
networkService.resumeQueue(); // 恢复队列
networkService.clearQueue();  // 清空队列

// 正常使用（自动队列管理）
final result = await networkService.get('/api/data');
```

## 配置参数

### RequestQueue 配置
- `maxConcurrency`: 最大并发请求数（默认：5）
- `maxQueueSize`: 最大队列大小（默认：100）

### 可调整位置
在 `DioNetworkService` 构造函数中：
```dart
final _requestQueue = RequestQueue(
  maxConcurrency: 5, // 可调整
  maxQueueSize: 100, // 可调整
);
```

## 向后兼容性

- 所有现有的 API 调用保持不变
- 队列功能是透明的，不影响现有代码
- 新功能是可选的，可以根据需要使用

## 测试建议

1. **基本功能测试**: 验证请求队列正常工作
2. **Token 刷新测试**: 模拟 token 过期场景
3. **并发测试**: 测试多个请求同时执行
4. **暂停/恢复测试**: 验证手动控制功能
5. **错误处理测试**: 测试各种错误场景

## 注意事项

1. **内存使用**: 队列会暂时存储请求，注意内存使用
2. **超时处理**: 队列中的请求仍然受到 Dio 超时设置影响
3. **错误传播**: 队列错误不会影响单个请求的错误处理
4. **线程安全**: 使用 synchronized 包确保线程安全

## 未来扩展

1. **动态优先级**: 根据请求类型自动设置优先级
2. **智能重试**: 基于错误类型的智能重试策略
3. **性能监控**: 更详细的性能指标和监控
4. **配置化**: 运行时动态调整队列参数
