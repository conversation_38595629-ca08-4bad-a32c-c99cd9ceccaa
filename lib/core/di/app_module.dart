import 'package:android_id/android_id.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_audio_room/features/audio_room/di/audio_room_module.dart';
import 'package:flutter_audio_room/features/authentication/di/authentication_module.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/user_info_cache_service.dart';
import 'package:flutter_audio_room/flavors.dart';
import 'package:flutter_audio_room/services/cache_service/cache_service.dart';
import 'package:flutter_audio_room/services/cache_service/i_cache_service.dart';
import 'package:flutter_audio_room/services/core_service/core_service.dart';
import 'package:flutter_audio_room/services/crypto_service/crypto_service.dart';
import 'package:flutter_audio_room/services/crypto_service/i_crypto_service.dart';
import 'package:flutter_audio_room/services/device_info/device_info_service.dart';
import 'package:flutter_audio_room/services/device_info/i_device_info_service.dart';
import 'package:flutter_audio_room/services/file_service/file_service.dart';
import 'package:flutter_audio_room/services/file_service/i_file_service.dart';
import 'package:flutter_audio_room/services/file_upload_service/file_upload_service.dart';
import 'package:flutter_audio_room/services/file_upload_service/i_file_upload_service.dart';
import 'package:flutter_audio_room/services/gift_service/di/gift_service_module.dart';
import 'package:flutter_audio_room/services/hive_service/hive_service.dart';
import 'package:flutter_audio_room/services/hive_service/i_hive_service.dart';
import 'package:flutter_audio_room/services/isar_service/i_isar_service.dart';
import 'package:flutter_audio_room/services/isar_service/isar_service.dart';
import 'package:flutter_audio_room/services/location_service/i_location_service.dart';
import 'package:flutter_audio_room/services/location_service/location_service.dart';
import 'package:flutter_audio_room/services/logger_service/i_logger_service.dart';
import 'package:flutter_audio_room/services/logger_service/logger_service.dart';
import 'package:flutter_audio_room/services/network_info_service/i_network_info_service.dart';
import 'package:flutter_audio_room/services/network_info_service/network_info_service.dart';
import 'package:flutter_audio_room/services/package_info/i_package_info_service.dart';
import 'package:flutter_audio_room/services/package_info/package_info_service.dart';
import 'package:flutter_audio_room/services/permission_service/permission_service.dart';
import 'package:flutter_audio_room/services/punishment_service/di/punishment_di.dart';
import 'package:flutter_audio_room/services/screen_protector_service/i_screen_protector_service.dart';
import 'package:flutter_audio_room/services/screen_protector_service/screen_protector_service.dart';
import 'package:flutter_audio_room/services/secure_storage_service/i_secure_storage_service.dart';
import 'package:flutter_audio_room/services/secure_storage_service/secure_store_service.dart';
import 'package:flutter_audio_room/services/session_key_service/di/session_key_module.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/signal_protocol_service.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/signal_protocol_service_impl.dart';
import 'package:flutter_audio_room/services/token_refresh/token_refresh_service.dart';
import 'package:flutter_audio_room/services/user_cache_service/di/user_cache_module.dart';
import 'package:flutter_audio_room/services/web_rtc_service/di/web_rtc_module.dart';
import 'package:flutter_audio_room/shared/data/local/shared_prefs_storage_service.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/data/remote/dio_network_service.dart';
import 'package:flutter_audio_room/shared/data/remote/network_service.dart';
import 'package:get_it/get_it.dart';
import 'package:pointycastle/pointycastle.dart';

import '../../services/websocket_service/di/websocket_module.dart';

final getIt = GetIt.instance;

/// Initialize all app dependencies
Future<void> initializeAppModule() async {
  // Setup GetIt service locator
  initializeBaseService();

  initPunishmentDI();

  initializeUserCacheModule();
  initializeAuthenticationModule();
  initializeAudioRoomModule();
  initializeGiftServiceModule();

  // Initialize WebSocket module
  initializeWebSocketModule();

  initializeWebRtcModule();

  await getIt.allReady();
}

void initializeBaseService() async {
  // logger service
  getIt.registerSingleton<ILoggerService>(LoggerService());

  // secure storage service
  getIt.registerSingleton<ISecureStorageService>(SecureStorageService());

  // screen protector service
  getIt.registerLazySingleton<IScreenProtectorService>(
    () => ScreenProtectorService(),
  );

  // crypto service
  getIt.registerSingletonAsync<ICryptoService>(() async {
    final secureStorage = getIt<ISecureStorageService>();
    final secureRandom = SecureRandom('Fortuna');
    final service = CryptoService(
      secureRandom: secureRandom,
      secureStorage: secureStorage,
    );
    await service.initialize();
    return service;
  });

  initSessionKeyModule();

  getIt.registerSingletonAsync<IFileService>(() async {
    final service = FileService();
    await service.initialize();
    return service;
  });

  // package info service
  getIt.registerSingletonAsync<IPackageInfoService>(() async {
    final service = PackageInfoService();
    await service.initialize();
    return service;
  });

  // device info service
  getIt.registerSingletonAsync<IDeviceInfoService>(() async {
    final deviceInfo = DeviceInfoPlugin();
    const androidIdPlugin = AndroidId();
    final secureStorage = getIt<ISecureStorageService>();
    final service = DeviceInfoService(
      deviceInfo: deviceInfo,
      secureStorage: secureStorage,
      androidIdPlugin: androidIdPlugin,
    );
    await service.initialize();
    return service;
  });

  // storage service
  getIt.registerSingletonAsync<StorageService>(() async {
    final service = SharedPrefsService();
    await service.init();
    return service;
  });

  getIt.registerSingletonAsync<IHiveService>(() async {
    final service = HiveService();
    await service.init();
    return service;
  });

  getIt.registerSingletonAsync<IIsarService>(() async {
    final service = IsarServiceImpl();
    await service.init();
    return service;
  });

  // token refresh service
  getIt.registerSingletonWithDependencies<TokenRefreshService>(() {
    final storageService = getIt<StorageService>();
    final deviceInfoService = getIt<IDeviceInfoService>();
    final packageInfoService = getIt<IPackageInfoService>();
    final dio = Dio();
    final service = TokenRefreshService(
      dio,
      storageService,
      deviceInfoService,
      packageInfoService,
    );
    return service;
  }, dependsOn: [StorageService, IDeviceInfoService, IPackageInfoService]);

  // dio network service
  getIt.registerSingletonWithDependencies<NetworkService>(() {
    final service = DioNetworkService(
      Dio(),
      getIt(),
      getIt(),
      getIt(),
      getIt<TokenRefreshService>(),
      F.connectionString,
    );
    return service;
  }, dependsOn: [TokenRefreshService, IDeviceInfoService, IPackageInfoService]);

  getIt.registerLazySingleton(() => PermissionService());

  getIt.registerLazySingleton(() => Connectivity());

  // Register network and location services
  getIt.registerSingletonWithDependencies<INetworkInfoService>(
    () => NetworkInfoService(getIt(), getIt()),
    dependsOn: [StorageService],
  );

  getIt.registerLazySingleton<ILocationService>(
    () => LocationService(),
  );

  getIt.registerLazySingleton<ICacheService>(() => CacheService());

  // User Info Cache Service
  getIt.registerLazySingleton<UserInfoCacheService>(
      () => UserInfoCacheService());

  // Signal Protocol Service
  getIt.registerLazySingleton<SignalProtocolService>(
    () => SignalProtocolServiceImpl(),
    dispose: (service) => service.dispose(),
  );

  getIt.registerSingletonWithDependencies<CoreService>(
    () => CoreService(getIt<NetworkService>(), getIt<StorageService>()),
    dependsOn: [NetworkService, StorageService],
  );

  getIt.registerSingletonWithDependencies<IFileUploadService>(
    () => FileUploadService(getIt<NetworkService>(), getIt()),
    dependsOn: [NetworkService],
  );
}
