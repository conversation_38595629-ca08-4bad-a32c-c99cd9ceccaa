import 'dart:async';
import 'dart:collection';

import 'package:dio/dio.dart';
import 'package:flutter_audio_room/core/mixins/exception_handler_mixin.dart';
import 'package:flutter_audio_room/services/device_info/i_device_info_service.dart';
import 'package:flutter_audio_room/services/package_info/i_package_info_service.dart';
import 'package:flutter_audio_room/services/token_refresh/token_refresh_service.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/data/remote/network_service.dart';
import 'package:flutter_audio_room/shared/data/remote/queue_aware_request_interceptor.dart';
import 'package:flutter_audio_room/shared/data/remote/retry_config.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/globals.dart';
import 'package:path/path.dart' as path;
import 'package:synchronized/synchronized.dart';

/// Request priority levels
enum RequestPriority {
  high,
  normal,
  low,
}

/// A request item in the queue
class RequestItem<T> {
  final Future<T> Function() request;
  final RequestPriority priority;
  final String? tag;
  final Completer<T> completer;

  RequestItem({
    required this.request,
    this.priority = RequestPriority.normal,
    this.tag,
  }) : completer = Completer<T>();
}

/// Handle HTTP request queue with priority support and concurrency control
class RequestQueue {
  static const int _defaultMaxQueueSize = 50;
  static const int _defaultMaxConcurrency = 3;

  final int maxQueueSize;
  final int maxConcurrency;

  final Queue<RequestItem> _queue = Queue<RequestItem>();
  int _activeRequests = 0;
  bool _isCancelled = false;
  bool _isPaused = false;
  final Lock _lock = Lock();

  // Error handler
  void Function(Object error, StackTrace stackTrace)? onError;

  // Token refresh callback
  void Function()? onTokenRefreshNeeded;

  RequestQueue({
    this.maxQueueSize = _defaultMaxQueueSize,
    this.maxConcurrency = _defaultMaxConcurrency,
    this.onError,
  });

  /// Add a request to the queue
  /// Returns a Future that completes when the request is executed
  Future<T> addRequest<T>(
    Future<T> Function() request, {
    RequestPriority priority = RequestPriority.normal,
    String? tag,
  }) async {
    return await _lock.synchronized(() async {
      if (_queue.length >= maxQueueSize) {
        throw Exception('Request queue is full');
      }

      final item = RequestItem<T>(
        request: request,
        priority: priority,
        tag: tag,
      );

      _addItemByPriority(item);
      _processQueue();

      return item.completer.future;
    });
  }

  void _addItemByPriority(RequestItem item) {
    if (_queue.isEmpty) {
      _queue.add(item);
      return;
    }

    // Convert to list for easier manipulation
    final list = _queue.toList();
    _queue.clear();

    // Find insertion point based on priority
    int insertIndex = list.length;
    for (int i = 0; i < list.length; i++) {
      if (item.priority.index < list[i].priority.index) {
        insertIndex = i;
        break;
      }
    }

    list.insert(insertIndex, item);
    _queue.addAll(list);
  }

  void _processQueue() {
    while (_queue.isNotEmpty &&
        _activeRequests < maxConcurrency &&
        !_isCancelled &&
        !_isPaused) {
      final item = _queue.removeFirst();
      _activeRequests++;

      _executeRequest(item);
    }
  }

  void _executeRequest(RequestItem item) {
    item.request().then((result) {
      if (!item.completer.isCompleted) {
        item.completer.complete(result);
      }
    }).catchError((error, stackTrace) {
      onError?.call(error, stackTrace);
      if (!item.completer.isCompleted) {
        item.completer.completeError(error, stackTrace);
      }
    }).whenComplete(() {
      _lock.synchronized(() {
        _activeRequests--;
        if (!_isCancelled) {
          _processQueue();
        }
      });
    });
  }

  void cancel() {
    _lock.synchronized(() {
      _isCancelled = true;
      // Complete all pending requests with cancellation error
      while (_queue.isNotEmpty) {
        final item = _queue.removeFirst();
        if (!item.completer.isCompleted) {
          item.completer.completeError(
            Exception('Request cancelled'),
            StackTrace.current,
          );
        }
      }
    });
  }

  void clear() {
    _lock.synchronized(() {
      while (_queue.isNotEmpty) {
        final item = _queue.removeFirst();
        if (!item.completer.isCompleted) {
          item.completer.completeError(
            Exception('Request queue cleared'),
            StackTrace.current,
          );
        }
      }
    });
  }

  void removeByTag(String tag) {
    _lock.synchronized(() {
      final itemsToRemove = <RequestItem>[];
      for (final item in _queue) {
        if (item.tag == tag) {
          itemsToRemove.add(item);
        }
      }
      for (final item in itemsToRemove) {
        _queue.remove(item);
        if (!item.completer.isCompleted) {
          item.completer.completeError(
            Exception('Request removed by tag: $tag'),
            StackTrace.current,
          );
        }
      }
    });
  }

  /// Pause the queue processing
  void pause() {
    _lock.synchronized(() {
      _isPaused = true;
    });
  }

  /// Resume the queue processing
  void resume() {
    _lock.synchronized(() {
      _isPaused = false;
      if (!_isCancelled) {
        _processQueue();
      }
    });
  }

  int get queueSize => _queue.length;
  int get activeRequests => _activeRequests;
  bool get isEmpty => _queue.isEmpty;
  bool get isCancelled => _isCancelled;
  bool get isPaused => _isPaused;
}

/// Manages token refresh coordination with request queue
class TokenRefreshManager {
  static TokenRefreshManager? _instance;
  static TokenRefreshManager get instance =>
      _instance ??= TokenRefreshManager._();

  TokenRefreshManager._();

  RequestQueue? _requestQueue;
  bool _isRefreshing = false;

  void setRequestQueue(RequestQueue queue) {
    _requestQueue = queue;
  }

  /// Called when token refresh is needed
  Future<void> onTokenRefreshNeeded() async {
    if (_isRefreshing || _requestQueue == null) return;

    _isRefreshing = true;

    // Pause the request queue
    _requestQueue!.pause();

    // The actual token refresh will be handled by RequestInterceptor
    // We just need to coordinate the queue pause/resume
  }

  /// Called when token refresh is completed (success or failure)
  void onTokenRefreshCompleted() {
    if (!_isRefreshing || _requestQueue == null) return;

    _isRefreshing = false;

    // Resume the request queue
    _requestQueue!.resume();
  }

  bool get isRefreshing => _isRefreshing;
}

class DioNetworkService extends NetworkService with ExceptionHandlerMixin {
  final Dio dio;
  final IDeviceInfoService _deviceInfoService;
  final IPackageInfoService _packageInfoService;
  final StorageService _storageService;
  final TokenRefreshService _tokenRefreshService;
  final String _baseUrl;

  // Add cancel token management
  final Map<String, CancelToken> _cancelTokens = {};
  final _lock = Lock();
  final _requestQueue = RequestQueue(
    maxConcurrency: 5, // Allow up to 5 concurrent requests
    maxQueueSize: 100, // Allow up to 100 queued requests
  );

  // Queue-aware interceptor
  late final QueueAwareRequestInterceptor _queueAwareInterceptor;

  DioNetworkService(
    this.dio,
    this._deviceInfoService,
    this._packageInfoService,
    this._storageService,
    this._tokenRefreshService,
    this._baseUrl,
  ) {
    // Initialize queue-aware interceptor
    _queueAwareInterceptor = QueueAwareRequestInterceptor(
      _tokenRefreshService,
      dio,
    );
    // Setup TokenRefreshManager with request queue
    TokenRefreshManager.instance.setRequestQueue(_requestQueue);

    // Setup request queue error handler
    _requestQueue.onError = (error, stackTrace) {
      // Handle queue errors - could integrate with logging service
      // For now, just ignore as individual requests will handle their own errors
    };

    // Setup token refresh callback
    _requestQueue.onTokenRefreshNeeded = () {
      TokenRefreshManager.instance.onTokenRefreshNeeded();
    };
    dio.options = BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: const Duration(seconds: 3),
      receiveTimeout: const Duration(seconds: 6),
      sendTimeout: const Duration(seconds: 3),
    );

    // Add retry interceptor first (should be before other interceptors)
    final retryConfig = RetryConfig.fromStrategy(RetryStrategy.defaultStrategy);
    dio.interceptors.add(retryConfig.createInterceptor(dio));

    // Add queue-aware token refresh interceptor
    dio.interceptors.add(_queueAwareInterceptor);

    // Add request interceptor to dynamically set headers
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        options.headers = headers;
        return handler.next(options);
      },
    ));
  }

  @override
  String get baseUrl => _baseUrl;

  @override
  Map<String, Object> get headers {
    final accessToken = _storageService.accessToken;
    final headers = {
      'accept': 'application/json',
      'content-type': 'application/json',
      'GK-platform': _deviceInfoService.platform,
      'GK-app-version': _packageInfoService.buildNumber,
    };

    return accessToken.isNotEmpty
        ? {
            ...headers,
            'Authorization': 'Bearer $accessToken',
          }
        : headers;
  }

  @override
  Map<String, dynamic>? updateHeader(Map<String, dynamic> data) {
    final header = {...headers, ...data};
    if (!kTestMode) {
      dio.options.headers = header;
    }
    return header;
  }

  /// Get or create a CancelToken for the given endpoint
  Future<CancelToken> _getOrCreateCancelToken(String endpoint) async {
    return await _lock.synchronized(() {
      if (!_cancelTokens.containsKey(endpoint)) {
        _cancelTokens[endpoint] = CancelToken();
      }
      return _cancelTokens[endpoint]!;
    });
  }

  @override
  Future<ResultWithData<Map<String, dynamic>>> post(String endpoint,
      {Map<String, dynamic>? data}) async {
    return await _requestQueue.addRequest<ResultWithData<Map<String, dynamic>>>(
      () async {
        final cancelToken = await _getOrCreateCancelToken(endpoint);
        try {
          final res = await handleException(
            () => dio.post(
              endpoint,
              data: data,
              cancelToken: cancelToken,
            ),
            endpoint: endpoint,
          );
          return res;
        } finally {
          await _lock.synchronized(() {
            _cancelTokens.remove(endpoint);
          });
        }
      },
      priority: RequestPriority.normal,
      tag: endpoint,
    );
  }

  @override
  Future<ResultWithData<Map<String, dynamic>>> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    return await _requestQueue.addRequest<ResultWithData<Map<String, dynamic>>>(
      () async {
        final cancelToken = await _getOrCreateCancelToken(endpoint);
        try {
          final res = await handleException(
            () => dio.get(
              endpoint,
              queryParameters: queryParameters,
              cancelToken: cancelToken,
            ),
            endpoint: endpoint,
          );
          return res;
        } finally {
          await _lock.synchronized(() {
            _cancelTokens.remove(endpoint);
          });
        }
      },
      priority: RequestPriority.normal,
      tag: endpoint,
    );
  }
  
  @override
  Future<ResultWithData<Map<String, dynamic>>> uploadFile(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    required String filePath,
    String? filename,
    String formName = 'file',
    Map<String, dynamic>? extraData,
    void Function(int count, int total)? onSendProgress,
  }) async {
    return await _requestQueue.addRequest<ResultWithData<Map<String, dynamic>>>(
      () async {
        final cancelToken = await _getOrCreateCancelToken(endpoint);
        try {
          // 获取文件名
          final actualFilename = filename ?? path.basename(filePath);

          // 创建FormData
          final Map<String, dynamic> formDataMap = {
            formName: await MultipartFile.fromFile(
              filePath,
              filename: actualFilename,
            ),
          };

          // 添加额外数据
          if (extraData != null) {
            formDataMap.addAll(extraData);
          }

          final formData = FormData.fromMap(formDataMap);

          // 临时修改header以支持文件上传
          final originalContentType = dio.options.headers['content-type'];
          dio.options.headers['content-type'] = 'multipart/form-data';

          final res = await handleException(
            () => dio.post(
              endpoint,
              data: formData,
              queryParameters: queryParameters,
              cancelToken: cancelToken,
              onSendProgress: onSendProgress,
            ),
            endpoint: endpoint,
          );

          // 恢复原始header
          dio.options.headers['content-type'] = originalContentType;

          return res;
        } finally {
          await _lock.synchronized(() {
            _cancelTokens.remove(endpoint);
          });
        }
      },
      priority: RequestPriority.normal,
      tag: endpoint,
    );
  }

  /// Cancel all ongoing requests
  @override
  Future<void> cancelAll() async {
    // Cancel all queued requests
    _requestQueue.cancel();

    // Cancel all active requests
    await _lock.synchronized(() {
      for (final token in _cancelTokens.values) {
        if (!token.isCancelled) {
          token.cancel('User cancelled all requests');
        }
      }
      _cancelTokens.clear();
    });
  }

  /// Cancel request for specific endpoint
  @override
  Future<void> cancelRequest(String endpoint) async {
    // Cancel queued requests with this endpoint tag
    _requestQueue.removeByTag(endpoint);

    // Cancel active request for this endpoint
    await _lock.synchronized(() {
      final token = _cancelTokens[endpoint];
      if (token != null && !token.isCancelled) {
        token.cancel('User cancelled request for $endpoint');
        _cancelTokens.remove(endpoint);
      }
    });
  }

  /// Get current queue status
  Map<String, dynamic> getQueueStatus() {
    return {
      'queueSize': _requestQueue.queueSize,
      'activeRequests': _requestQueue.activeRequests,
      'isEmpty': _requestQueue.isEmpty,
      'isCancelled': _requestQueue.isCancelled,
      'isPaused': _requestQueue.isPaused,
      'tokenRefreshInProgress': TokenRefreshManager.instance.isRefreshing,
    };
  }

  /// Clear all queued requests
  void clearQueue() {
    _requestQueue.clear();
  }

  /// Pause the request queue
  void pauseQueue() {
    _requestQueue.pause();
  }

  /// Resume the request queue
  void resumeQueue() {
    _requestQueue.resume();
  }
}
