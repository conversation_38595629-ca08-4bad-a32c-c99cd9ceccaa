import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/shared/data/remote/dio_network_service.dart';
import 'package:flutter_audio_room/shared/data/remote/network_service.dart';

/// Example usage of the new request queue functionality
class RequestQueueExample {
  
  /// Demonstrate basic queue functionality
  static Future<void> demonstrateBasicQueue() async {
    final networkService = getIt<NetworkService>() as DioNetworkService;
    
    print('=== Request Queue Demo ===');
    
    // Check initial queue status
    print('Initial queue status: ${networkService.getQueueStatus()}');
    
    // Make multiple concurrent requests
    final futures = <Future>[];
    
    for (int i = 0; i < 5; i++) {
      futures.add(
        networkService.get('/api/test/$i').then((result) {
          result.fold(
            (error) => print('Request $i failed: ${error.message}'),
            (data) => print('Request $i succeeded'),
          );
        }).catchError((error) {
          print('Request $i error: $error');
        })
      );
    }
    
    // Check queue status while requests are processing
    await Future.delayed(const Duration(milliseconds: 100));
    print('Queue status during processing: ${networkService.getQueueStatus()}');
    
    // Wait for all requests to complete
    await Future.wait(futures);
    
    // Check final queue status
    print('Final queue status: ${networkService.getQueueStatus()}');
  }
  
  /// Demonstrate queue pause/resume functionality
  static Future<void> demonstrateQueueControl() async {
    final networkService = getIt<NetworkService>() as DioNetworkService;
    
    print('\n=== Queue Control Demo ===');
    
    // Pause the queue
    networkService.pauseQueue();
    print('Queue paused');
    
    // Add requests while paused
    final futures = <Future>[];
    for (int i = 0; i < 3; i++) {
      futures.add(
        networkService.get('/api/paused-test/$i').then((result) {
          result.fold(
            (error) => print('Paused request $i failed: ${error.message}'),
            (data) => print('Paused request $i succeeded'),
          );
        }).catchError((error) {
          print('Paused request $i error: $error');
        })
      );
    }
    
    await Future.delayed(const Duration(milliseconds: 500));
    print('Queue status while paused: ${networkService.getQueueStatus()}');
    
    // Resume the queue
    networkService.resumeQueue();
    print('Queue resumed');
    
    // Wait for requests to complete
    await Future.wait(futures);
    print('All paused requests completed');
  }
  
  /// Demonstrate priority queue functionality
  static Future<void> demonstratePriorityQueue() async {
    final networkService = getIt<NetworkService>() as DioNetworkService;
    
    print('\n=== Priority Queue Demo ===');
    
    // Note: Priority functionality would need to be exposed through NetworkService interface
    // For now, this is just a placeholder to show the concept
    
    print('Priority queue functionality is available at the RequestQueue level');
    print('High priority requests will be processed before normal/low priority ones');
    print('Current queue status: ${networkService.getQueueStatus()}');
  }
  
  /// Demonstrate token refresh coordination
  static Future<void> demonstrateTokenRefreshCoordination() async {
    final networkService = getIt<NetworkService>() as DioNetworkService;
    
    print('\n=== Token Refresh Coordination Demo ===');
    
    print('When a token refresh is needed:');
    print('1. The request queue will be automatically paused');
    print('2. Token refresh will be performed');
    print('3. The queue will be resumed after refresh completes');
    print('4. Queued requests will use the new token');
    
    print('Current queue status: ${networkService.getQueueStatus()}');
    
    // In a real scenario, token refresh would be triggered by 401 responses
    // This is handled automatically by the QueueAwareRequestInterceptor
  }
  
  /// Run all demonstrations
  static Future<void> runAllDemos() async {
    try {
      await demonstrateBasicQueue();
      await demonstrateQueueControl();
      await demonstratePriorityQueue();
      await demonstrateTokenRefreshCoordination();
      
      print('\n=== All demos completed ===');
    } catch (e) {
      print('Demo error: $e');
    }
  }
}

/// Usage instructions for the new request queue system
class RequestQueueUsageGuide {
  
  static void printUsageInstructions() {
    print('''
=== Request Queue Usage Guide ===

1. BASIC USAGE:
   - All HTTP requests (GET, POST, uploadFile) now go through a queue
   - Queue supports up to 5 concurrent requests by default
   - Queue can hold up to 100 pending requests

2. QUEUE MANAGEMENT:
   - getQueueStatus(): Get current queue statistics
   - pauseQueue(): Pause processing new requests
   - resumeQueue(): Resume processing requests
   - clearQueue(): Clear all pending requests
   - cancelAll(): Cancel all requests (active and queued)
   - cancelRequest(endpoint): Cancel specific endpoint requests

3. TOKEN REFRESH INTEGRATION:
   - When token expires, queue automatically pauses
   - Token refresh happens in background
   - Queue resumes after successful refresh
   - Failed requests are retried with new token
   - No duplicate token refresh requests

4. PRIORITY SUPPORT (Internal):
   - RequestPriority.high: Processed first
   - RequestPriority.normal: Default priority
   - RequestPriority.low: Processed last

5. ERROR HANDLING:
   - Individual request errors are handled normally
   - Queue errors are logged but don't affect other requests
   - Token refresh failures clear the queue appropriately

6. MONITORING:
   - Queue status includes: size, active requests, paused state
   - Token refresh status is also available
   - Useful for debugging and performance monitoring
''');
  }
}
